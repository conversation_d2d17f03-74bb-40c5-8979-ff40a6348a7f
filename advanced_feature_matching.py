#!/usr/bin/env python3
"""
Advanced Feature Matching Analysis
Look for scaled, normalized, and transformed versions of our features
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

def load_enhanced_data():
    """Load our enhanced dataset with technical indicators"""
    return pd.read_parquet('train_with_technical_indicators.parquet')

def advanced_feature_matching(df, sample_size=2000):
    """
    Advanced matching including scaled and transformed versions
    """
    print("ADVANCED FEATURE MATCHING ANALYSIS")
    print("="*50)
    
    # Get feature sets
    original_cols = ['bid_qty', 'ask_qty', 'buy_qty', 'sell_qty', 'volume', 'label']
    tech_features = [col for col in df.columns 
                    if col not in original_cols and not col.startswith('X')]
    x_features = [col for col in df.columns if col.startswith('X')]
    
    print(f"Analyzing {len(tech_features)} technical features vs {len(x_features)} X features")
    
    # Use a larger sample for better analysis
    sample_df = df.iloc[:sample_size].copy()
    
    matches = []
    
    # Test different transformations
    transformations = {
        'raw': lambda x: x,
        'standardized': lambda x: StandardScaler().fit_transform(x.values.reshape(-1, 1)).flatten(),
        'minmax': lambda x: MinMaxScaler().fit_transform(x.values.reshape(-1, 1)).flatten(),
        'robust': lambda x: RobustScaler().fit_transform(x.values.reshape(-1, 1)).flatten(),
        'log': lambda x: np.log(np.abs(x) + 1e-8) * np.sign(x),
        'squared': lambda x: x ** 2,
        'sqrt': lambda x: np.sqrt(np.abs(x)) * np.sign(x)
    }
    
    print(f"Testing {len(transformations)} different transformations...")
    
    # Check a subset of features for speed
    tech_subset = tech_features[:20]  # First 20 technical features
    x_subset = x_features[:50]        # First 50 X features
    
    for tech_feat in tech_subset:
        tech_values = sample_df[tech_feat].dropna()
        if len(tech_values) < 100:
            continue
            
        print(f"Analyzing {tech_feat}...")
        
        for transform_name, transform_func in transformations.items():
            try:
                tech_transformed = transform_func(tech_values)
                
                for x_feat in x_subset:
                    x_values = sample_df[x_feat].iloc[:len(tech_transformed)]
                    
                    # Calculate correlations
                    pearson_corr, pearson_p = pearsonr(tech_transformed, x_values)
                    spearman_corr, spearman_p = spearmanr(tech_transformed, x_values)
                    
                    # Check for strong correlations
                    if abs(pearson_corr) > 0.8 or abs(spearman_corr) > 0.8:
                        matches.append({
                            'tech_feature': tech_feat,
                            'x_feature': x_feat,
                            'transformation': transform_name,
                            'pearson_corr': pearson_corr,
                            'spearman_corr': spearman_corr,
                            'pearson_p': pearson_p,
                            'spearman_p': spearman_p
                        })
            except:
                continue
    
    return matches

def analyze_x_feature_patterns(df):
    """
    Analyze patterns in X features to understand their nature
    """
    print("\nX FEATURE PATTERN ANALYSIS")
    print("="*40)
    
    x_features = [col for col in df.columns if col.startswith('X')]
    sample_df = df.iloc[:5000]  # Use larger sample
    
    # Analyze correlations between X features
    x_corr_matrix = sample_df[x_features[:50]].corr()
    
    # Find highly correlated X features
    high_corr_pairs = []
    for i in range(len(x_corr_matrix.columns)):
        for j in range(i+1, len(x_corr_matrix.columns)):
            corr_val = x_corr_matrix.iloc[i, j]
            if abs(corr_val) > 0.9:
                high_corr_pairs.append({
                    'feature1': x_corr_matrix.columns[i],
                    'feature2': x_corr_matrix.columns[j],
                    'correlation': corr_val
                })
    
    print(f"Found {len(high_corr_pairs)} highly correlated X feature pairs (|r| > 0.9)")
    
    # Analyze X feature distributions
    print("\nX Feature Distribution Analysis:")
    x_stats = sample_df[x_features[:20]].describe()
    
    # Check for standardized features (mean~0, std~1)
    standardized_features = []
    for feat in x_features[:50]:
        mean_val = sample_df[feat].mean()
        std_val = sample_df[feat].std()
        if abs(mean_val) < 0.1 and 0.8 < std_val < 1.2:
            standardized_features.append(feat)
    
    print(f"Potentially standardized features (mean~0, std~1): {len(standardized_features)}")
    if standardized_features[:5]:
        print(f"Examples: {standardized_features[:5]}")
    
    # Check for bounded features (might be normalized)
    bounded_features = []
    for feat in x_features[:50]:
        min_val = sample_df[feat].min()
        max_val = sample_df[feat].max()
        if -1.1 < min_val < -0.9 and 0.9 < max_val < 1.1:
            bounded_features.append(feat)
    
    print(f"Potentially normalized features (range ~[-1,1]): {len(bounded_features)}")
    
    return high_corr_pairs, standardized_features, bounded_features

def correlation_with_target_analysis(df):
    """
    Analyze which features (both technical and X) correlate best with target
    """
    print("\nTARGET CORRELATION ANALYSIS")
    print("="*40)
    
    sample_df = df.iloc[:10000]  # Larger sample for better correlation estimates
    
    # Get all features except target
    all_features = [col for col in sample_df.columns if col != 'label']
    
    # Calculate correlations with target
    correlations = []
    for feat in all_features:
        try:
            corr, p_val = pearsonr(sample_df[feat].dropna(), 
                                 sample_df['label'].iloc[:len(sample_df[feat].dropna())])
            correlations.append({
                'feature': feat,
                'correlation': corr,
                'p_value': p_val,
                'abs_correlation': abs(corr),
                'feature_type': 'technical' if not feat.startswith('X') else 'X_feature'
            })
        except:
            continue
    
    # Sort by absolute correlation
    correlations.sort(key=lambda x: x['abs_correlation'], reverse=True)
    
    print("Top 10 features correlated with target:")
    for i, corr_info in enumerate(correlations[:10]):
        print(f"{i+1:2d}. {corr_info['feature']:<20} | r={corr_info['correlation']:7.4f} | type={corr_info['feature_type']}")
    
    # Compare technical vs X features
    tech_corrs = [c['abs_correlation'] for c in correlations if c['feature_type'] == 'technical']
    x_corrs = [c['abs_correlation'] for c in correlations if c['feature_type'] == 'X_feature']
    
    print(f"\nCorrelation strength comparison:")
    print(f"Technical features - Mean: {np.mean(tech_corrs):.4f}, Max: {np.max(tech_corrs):.4f}")
    print(f"X features - Mean: {np.mean(x_corrs):.4f}, Max: {np.max(x_corrs):.4f}")
    
    return correlations

def main():
    """
    Main analysis function
    """
    print("ADVANCED FEATURE REVERSE ENGINEERING")
    print("="*60)
    
    # Load enhanced data
    df = load_enhanced_data()
    print(f"Loaded enhanced dataset: {df.shape}")
    
    # Advanced feature matching
    matches = advanced_feature_matching(df)
    
    print(f"\n{'='*60}")
    print("ADVANCED MATCHING RESULTS")
    print(f"{'='*60}")
    
    if matches:
        print(f"Found {len(matches)} potential matches with transformations!")
        
        # Sort by strongest correlation
        matches.sort(key=lambda x: max(abs(x['pearson_corr']), abs(x['spearman_corr'])), reverse=True)
        
        print("\nTop matches:")
        for i, match in enumerate(matches[:10]):
            print(f"{i+1:2d}. {match['tech_feature']} → {match['x_feature']}")
            print(f"     Transformation: {match['transformation']}")
            print(f"     Pearson: {match['pearson_corr']:.4f} (p={match['pearson_p']:.6f})")
            print(f"     Spearman: {match['spearman_corr']:.4f}")
            print()
    else:
        print("No strong matches found even with transformations.")
    
    # Analyze X feature patterns
    high_corr_pairs, standardized_features, bounded_features = analyze_x_feature_patterns(df)
    
    # Target correlation analysis
    correlations = correlation_with_target_analysis(df)
    
    return matches, correlations

if __name__ == "__main__":
    feature_matches, target_correlations = main()
