# Data files (too large for GitHub)
*.parquet
*.csv
*.pkl
*.joblib

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/

# VS Code
.vscode/

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Model files (can be large)
*.model
*.h5
*.hdf5

# Temporary files
*.tmp
*.temp

# Images (keep analysis plots but exclude large images)
# Uncomment if you want to exclude all images
# *.png
# *.jpg
# *.jpeg

# Keep important files
!README.md
!*.py
!*.ipynb
!requirements.txt
