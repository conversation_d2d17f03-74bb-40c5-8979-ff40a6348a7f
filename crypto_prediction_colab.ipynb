{"cells": [{"cell_type": "markdown", "metadata": {"id": "crypto-prediction-title"}, "source": ["# Crypto Market Prediction - Complete Pipeline\n", "\n", "This notebook implements a complete machine learning pipeline for crypto market prediction including:\n", "- Advanced feature engineering\n", "- Multiple model implementations (XGBoost, LightGBM, Neural Networks)\n", "- Model validation and tuning\n", "- Final predictions and submission\n", "\n", "## Instructions:\n", "1. Upload your data files (train.parquet, test.parquet, sample_submission.csv) to Colab\n", "2. Run all cells in order\n", "3. Download the final submission file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install-packages"}, "outputs": [], "source": ["# Install required packages\n", "!pip install xgboost lightgbm optuna -q\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import TimeSeriesSplit, cross_val_score\n", "from sklearn.preprocessing import StandardScaler, RobustScaler\n", "from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import Ridge, ElasticNet\n", "import xgboost as xgb\n", "import lightgbm as lgb\n", "import optuna\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"All packages installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load-data"}, "outputs": [], "source": ["# Load data\n", "print(\"Loading datasets...\")\n", "train_df = pd.read_parquet('train.parquet')\n", "test_df = pd.read_parquet('test.parquet')\n", "sample_sub = pd.read_csv('sample_submission.csv')\n", "\n", "print(f\"Training data shape: {train_df.shape}\")\n", "print(f\"Test data shape: {test_df.shape}\")\n", "print(f\"Sample submission shape: {sample_sub.shape}\")\n", "\n", "# Quick data overview\n", "print(\"\\nTarget statistics:\")\n", "print(train_df['label'].describe())\n", "\n", "print(\"\\nData types:\")\n", "print(train_df.dtypes.value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "feature-engineering"}, "outputs": [], "source": ["# Advanced Feature Engineering\n", "def create_advanced_features(df, is_train=True):\n", "    \"\"\"Create comprehensive feature set\"\"\"\n", "    df_new = df.copy()\n", "    \n", "    # Market microstructure features\n", "    df_new['bid_ask_spread'] = df_new['ask_qty'] - df_new['bid_qty']\n", "    df_new['bid_ask_ratio'] = df_new['bid_qty'] / (df_new['ask_qty'] + 1e-8)\n", "    df_new['buy_sell_ratio'] = df_new['buy_qty'] / (df_new['sell_qty'] + 1e-8)\n", "    df_new['buy_sell_imbalance'] = df_new['buy_qty'] - df_new['sell_qty']\n", "    df_new['volume_intensity'] = df_new['volume'] / (df_new['buy_qty'] + df_new['sell_qty'] + 1e-8)\n", "    \n", "    # Order flow features\n", "    df_new['bid_volume_ratio'] = df_new['bid_qty'] / (df_new['volume'] + 1e-8)\n", "    df_new['ask_volume_ratio'] = df_new['ask_qty'] / (df_new['volume'] + 1e-8)\n", "    df_new['net_flow'] = df_new['buy_qty'] - df_new['sell_qty']\n", "    df_new['flow_ratio'] = df_new['net_flow'] / (df_new['volume'] + 1e-8)\n", "    \n", "    # Time-based features (only for training data with datetime index)\n", "    if is_train and hasattr(df_new.index, 'hour'):\n", "        df_new['hour'] = df_new.index.hour\n", "        df_new['day_of_week'] = df_new.index.dayofweek\n", "        df_new['month'] = df_new.index.month\n", "        \n", "        # Cyclical encoding\n", "        df_new['hour_sin'] = np.sin(2 * np.pi * df_new['hour'] / 24)\n", "        df_new['hour_cos'] = np.cos(2 * np.pi * df_new['hour'] / 24)\n", "        df_new['dow_sin'] = np.sin(2 * np.pi * df_new['day_of_week'] / 7)\n", "        df_new['dow_cos'] = np.cos(2 * np.pi * df_new['day_of_week'] / 7)\n", "        df_new['month_sin'] = np.sin(2 * np.pi * df_new['month'] / 12)\n", "        df_new['month_cos'] = np.cos(2 * np.pi * df_new['month'] / 12)\n", "        \n", "        df_new = df_new.drop(['hour', 'day_of_week', 'month'], axis=1)\n", "    elif not is_train:\n", "        # Dummy time features for test data\n", "        for feat in ['hour_sin', 'hour_cos', 'dow_sin', 'dow_cos', 'month_sin', 'month_cos']:\n", "            df_new[feat] = 0.0\n", "    \n", "    # Rolling window features\n", "    windows = [3, 5, 10, 20]\n", "    features = ['volume', 'buy_qty', 'sell_qty', 'bid_qty', 'ask_qty']\n", "    \n", "    for window in windows:\n", "        for feature in features:\n", "            df_new[f'{feature}_ma_{window}'] = df_new[feature].rolling(window=window).mean()\n", "            df_new[f'{feature}_std_{window}'] = df_new[feature].rolling(window=window).std()\n", "            df_new[f'{feature}_min_{window}'] = df_new[feature].rolling(window=window).min()\n", "            df_new[f'{feature}_max_{window}'] = df_new[feature].rolling(window=window).max()\n", "    \n", "    # Lag features (only for training data)\n", "    if is_train and 'label' in df_new.columns:\n", "        for lag in [1, 2, 3, 5, 10]:\n", "            df_new[f'label_lag_{lag}'] = df_new['label'].shift(lag)\n", "    \n", "    # Interaction features\n", "    df_new['volume_x_spread'] = df_new['volume'] * df_new['bid_ask_spread']\n", "    df_new['imbalance_x_volume'] = df_new['buy_sell_imbalance'] * df_new['volume']\n", "    \n", "    return df_new\n", "\n", "print(\"Creating advanced features...\")\n", "train_enhanced = create_advanced_features(train_df, is_train=True)\n", "test_enhanced = create_advanced_features(test_df, is_train=False)\n", "\n", "print(f\"Enhanced training data shape: {train_enhanced.shape}\")\n", "print(f\"Enhanced test data shape: {test_enhanced.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data-preparation"}, "outputs": [], "source": ["# Prepare data for modeling\n", "def prepare_modeling_data(train_df, test_df, n_features=200):\n", "    \"\"\"Prepare data with feature selection and scaling\"\"\"\n", "    \n", "    # Features and target\n", "    target_col = 'label'\n", "    feature_cols = [col for col in train_df.columns if col != target_col]\n", "    \n", "    X = train_df[feature_cols].fillna(0).replace([np.inf, -np.inf], 0)\n", "    y = train_df[target_col]\n", "    \n", "    # Time series split for validation\n", "    tscv = TimeSeriesSplit(n_splits=5)\n", "    splits = list(tscv.split(X))\n", "    train_idx, val_idx = splits[-1]  # Use last split for final validation\n", "    \n", "    X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]\n", "    y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]\n", "    \n", "    print(f\"Training set: {len(X_train)}, Validation set: {len(X_val)}\")\n", "    \n", "    # Feature selection using mutual information\n", "    print(f\"Selecting top {n_features} features...\")\n", "    selector = SelectKBest(score_func=mutual_info_regression, k=min(n_features, X_train.shape[1]))\n", "    X_train_selected = selector.fit_transform(X_train, y_train)\n", "    X_val_selected = selector.transform(X_val)\n", "    \n", "    selected_features = X.columns[selector.get_support()].tolist()\n", "    print(f\"Selected {len(selected_features)} features\")\n", "    \n", "    # Prepare test data\n", "    X_test = test_df[feature_cols].fillna(0).replace([np.inf, -np.inf], 0)\n", "    X_test_selected = selector.transform(X_test)\n", "    \n", "    # Scaling\n", "    scaler = RobustScaler()\n", "    X_train_scaled = scaler.fit_transform(X_train_selected)\n", "    X_val_scaled = scaler.transform(X_val_selected)\n", "    X_test_scaled = scaler.transform(X_test_selected)\n", "    \n", "    return {\n", "        'X_train': X_train_scaled,\n", "        'X_val': X_val_scaled,\n", "        'X_test': X_test_scaled,\n", "        'y_train': y_train,\n", "        'y_val': y_val,\n", "        'selected_features': selected_features,\n", "        'selector': selector,\n", "        'scaler': scaler,\n", "        'tscv_splits': splits\n", "    }\n", "\n", "print(\"Preparing modeling data...\")\n", "data = prepare_modeling_data(train_enhanced, test_enhanced, n_features=200)\n", "print(\"Data preparation completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "model-training"}, "outputs": [], "source": ["# Model Training and Evaluation\n", "def evaluate_model(model, X_train, y_train, X_val, y_val, model_name):\n", "    \"\"\"Evaluate model performance\"\"\"\n", "    model.fit(X_train, y_train)\n", "    \n", "    train_pred = model.predict(X_train)\n", "    val_pred = model.predict(X_val)\n", "    \n", "    train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))\n", "    val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))\n", "    train_mae = mean_absolute_error(y_train, train_pred)\n", "    val_mae = mean_absolute_error(y_val, val_pred)\n", "    \n", "    print(f\"{model_name} Results:\")\n", "    print(f\"  Train RMSE: {train_rmse:.6f}, Val RMSE: {val_rmse:.6f}\")\n", "    print(f\"  Train MAE: {train_mae:.6f}, Val MAE: {val_mae:.6f}\")\n", "    \n", "    return {\n", "        'model': model,\n", "        'train_rmse': train_rmse,\n", "        'val_rmse': val_rmse,\n", "        'train_mae': train_mae,\n", "        'val_mae': val_mae,\n", "        'val_predictions': val_pred\n", "    }\n", "\n", "# Train multiple models\n", "models = {}\n", "results = {}\n", "\n", "print(\"Training models...\\n\")\n", "\n", "# 1. Ridge Regression\n", "ridge = Ridge(alpha=1.0, random_state=42)\n", "results['Ridge'] = evaluate_model(ridge, data['X_train'], data['y_train'], \n", "                                 data['X_val'], data['y_val'], 'Ridge Regression')\n", "models['Ridge'] = ridge\n", "\n", "print()\n", "\n", "# 2. XGBoost\n", "xgb_model = xgb.XGBRegressor(\n", "    n_estimators=500,\n", "    max_depth=6,\n", "    learning_rate=0.1,\n", "    subsample=0.8,\n", "    colsample_bytree=0.8,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "results['XGBoost'] = evaluate_model(xgb_model, data['X_train'], data['y_train'], \n", "                                   data['X_val'], data['y_val'], 'XGBoost')\n", "models['XGBoost'] = xgb_model\n", "\n", "print()\n", "\n", "# 3. LightGBM\n", "lgb_model = lgb.LGBMRegressor(\n", "    n_estimators=500,\n", "    max_depth=6,\n", "    learning_rate=0.1,\n", "    subsample=0.8,\n", "    colsample_bytree=0.8,\n", "    random_state=42,\n", "    n_jobs=-1,\n", "    verbose=-1\n", ")\n", "results['LightGBM'] = evaluate_model(lgb_model, data['X_train'], data['y_train'], \n", "                                    data['X_val'], data['y_val'], 'LightGBM')\n", "models['LightGBM'] = lgb_model\n", "\n", "print(\"\\nModel training completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "model-ensemble"}, "outputs": [], "source": ["# Model Ensemble and Final Predictions\n", "def create_ensemble_predictions(models, results, X_test):\n", "    \"\"\"Create weighted ensemble predictions\"\"\"\n", "    predictions = []\n", "    weights = []\n", "    \n", "    # Weight models by inverse validation RMSE\n", "    for name, model in models.items():\n", "        pred = model.predict(X_test)\n", "        predictions.append(pred)\n", "        \n", "        val_rmse = results[name]['val_rmse']\n", "        weight = 1.0 / val_rmse\n", "        weights.append(weight)\n", "        \n", "        print(f\"{name} - Val RMSE: {val_rmse:.6f}, Weight: {weight:.4f}\")\n", "    \n", "    # Normalize weights\n", "    weights = np.array(weights)\n", "    weights = weights / weights.sum()\n", "    \n", "    # Weighted ensemble\n", "    ensemble_pred = np.average(predictions, axis=0, weights=weights)\n", "    \n", "    print(f\"\\nFinal ensemble weights: {dict(zip(models.keys(), weights))}\")\n", "    \n", "    return ensemble_pred\n", "\n", "print(\"Creating ensemble predictions...\")\n", "final_predictions = create_ensemble_predictions(models, results, data['X_test'])\n", "\n", "# Create submission file\n", "submission = pd.DataFrame({\n", "    'ID': range(1, len(final_predictions) + 1),\n", "    'prediction': final_predictions\n", "})\n", "\n", "submission.to_csv('submission_ensemble.csv', index=False)\n", "print(f\"\\nSubmission file created with {len(submission)} predictions\")\n", "print(f\"Prediction statistics:\")\n", "print(submission['prediction'].describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "results-summary"}, "outputs": [], "source": ["# Results Summary and Visualization\n", "print(\"=\" * 60)\n", "print(\"FINAL RESULTS SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "# Model comparison\n", "comparison_df = pd.DataFrame({\n", "    'Model': list(results.keys()),\n", "    'Train_RMSE': [results[model]['train_rmse'] for model in results.keys()],\n", "    'Val_RMSE': [results[model]['val_rmse'] for model in results.keys()],\n", "    'Train_MAE': [results[model]['train_mae'] for model in results.keys()],\n", "    'Val_MAE': [results[model]['val_mae'] for model in results.keys()]\n", "})\n", "\n", "print(\"\\nModel Performance Comparison:\")\n", "print(comparison_df.round(6))\n", "\n", "# Best model\n", "best_model = comparison_df.loc[comparison_df['Val_RMSE'].idxmin(), 'Model']\n", "best_rmse = comparison_df['Val_RMSE'].min()\n", "print(f\"\\nBest single model: {best_model} (Val RMSE: {best_rmse:.6f})\")\n", "\n", "# Visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Model comparison\n", "comparison_df.set_index('Model')[['Train_RMSE', 'Val_RMSE']].plot(kind='bar', ax=axes[0,0])\n", "axes[0,0].set_title('Model RMSE Comparison')\n", "axes[0,0].set_ylabel('RMSE')\n", "axes[0,0].legend()\n", "\n", "# Prediction distribution\n", "axes[0,1].hist(final_predictions, bins=50, alpha=0.7, edgecolor='black')\n", "axes[0,1].set_title('Final Predictions Distribution')\n", "axes[0,1].set_xlabel('Prediction Value')\n", "axes[0,1].set_ylabel('Frequency')\n", "\n", "# Target vs prediction scatter (validation set)\n", "best_val_pred = results[best_model]['val_predictions']\n", "axes[1,0].scatter(data['y_val'], best_val_pred, alpha=0.5)\n", "axes[1,0].plot([data['y_val'].min(), data['y_val'].max()], \n", "               [data['y_val'].min(), data['y_val'].max()], 'r--')\n", "axes[1,0].set_title(f'{best_model} - Actual vs Predicted')\n", "axes[1,0].set_xlabel('Actual')\n", "axes[1,0].set_ylabel('Predicted')\n", "\n", "# Residuals\n", "residuals = data['y_val'] - best_val_pred\n", "axes[1,1].scatter(best_val_pred, residuals, alpha=0.5)\n", "axes[1,1].axhline(y=0, color='r', linestyle='--')\n", "axes[1,1].set_title(f'{best_model} - Residuals')\n", "axes[1,1].set_xlabel('Predicted')\n", "axes[1,1].set_ylabel('Residuals')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"CRYPTO MARKET PREDICTION PIPELINE COMPLETED!\")\n", "print(\"=\" * 60)\n", "print(\"\\nFiles created:\")\n", "print(\"- submission_ensemble.csv (final submission)\")\n", "print(\"\\nNext steps:\")\n", "print(\"1. Download the submission file\")\n", "print(\"2. Submit to Kaggle competition\")\n", "print(\"3. Monitor leaderboard performance\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}