#!/usr/bin/env python3
"""
Summary of Feature Engineering Insights
"""

import pandas as pd
import numpy as np
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def detailed_feature_analysis():
    """
    Detailed analysis of our findings
    """
    print("FEATURE ENGINEERING INSIGHTS SUMMARY")
    print("="*60)
    
    # Load data
    df = pd.read_parquet('train_with_technical_indicators.parquet')
    sample_df = df.iloc[:10000]  # Use 10k samples for analysis
    
    print(f"Dataset: {df.shape[0]:,} rows, {df.shape[1]} columns")
    
    # === TECHNICAL FEATURES PERFORMANCE ===
    print("\n1. TECHNICAL FEATURES PERFORMANCE")
    print("-" * 40)
    
    tech_features = [col for col in df.columns 
                    if not col.startswith('X') and col not in 
                    ['bid_qty', 'ask_qty', 'buy_qty', 'sell_qty', 'volume', 'label']]
    
    # Calculate correlations for technical features
    tech_correlations = []
    for feat in tech_features:
        try:
            clean_data = sample_df[[feat, 'label']].dropna()
            if len(clean_data) > 100:
                corr, p_val = pearsonr(clean_data[feat], clean_data['label'])
                tech_correlations.append({
                    'feature': feat,
                    'correlation': corr,
                    'abs_correlation': abs(corr),
                    'p_value': p_val
                })
        except:
            continue
    
    tech_correlations.sort(key=lambda x: x['abs_correlation'], reverse=True)
    
    print("Top 10 Technical Features by Target Correlation:")
    for i, tc in enumerate(tech_correlations[:10]):
        print(f"{i+1:2d}. {tc['feature']:<25} | r={tc['correlation']:7.4f} | p={tc['p_value']:.6f}")
    
    # === X FEATURES PERFORMANCE ===
    print("\n2. X FEATURES PERFORMANCE")
    print("-" * 40)
    
    x_features = [col for col in df.columns if col.startswith('X')]
    
    # Get top X features (from previous analysis)
    top_x_features = ['X17', 'X37', 'X39', 'X35', 'X185', 'X41', 'X188', 'X33', 'X191', 'X415']
    
    print("Top 10 X Features by Target Correlation:")
    for i, feat in enumerate(top_x_features):
        clean_data = sample_df[[feat, 'label']].dropna()
        corr, p_val = pearsonr(clean_data[feat], clean_data['label'])
        print(f"{i+1:2d}. {feat:<10} | r={corr:7.4f} | p={p_val:.6f}")
    
    # === FEATURE COMPARISON ===
    print("\n3. FEATURE TYPE COMPARISON")
    print("-" * 40)
    
    best_tech_corr = tech_correlations[0]['abs_correlation'] if tech_correlations else 0
    best_x_corr = 0.3727  # X17 correlation from previous analysis
    
    print(f"Best Technical Feature Correlation: {best_tech_corr:.4f}")
    print(f"Best X Feature Correlation:         {best_x_corr:.4f}")
    print(f"X Features are {best_x_corr/best_tech_corr:.1f}x stronger!")
    
    # === FEATURE ENGINEERING INSIGHTS ===
    print("\n4. KEY INSIGHTS")
    print("-" * 40)
    
    print("✓ X Features are significantly more predictive than basic technical indicators")
    print("✓ No direct matches found between our features and X features")
    print("✓ X features likely use:")
    print("  - More sophisticated time windows")
    print("  - Complex feature interactions")
    print("  - Advanced mathematical transformations")
    print("  - Proprietary domain knowledge")
    
    # === RECOMMENDATIONS ===
    print("\n5. FEATURE ENGINEERING RECOMMENDATIONS")
    print("-" * 40)
    
    print("🎯 Focus on X features for modeling (they're much stronger)")
    print("🔧 Enhance technical features with:")
    print("   - Longer time windows (60min, 4hr, daily)")
    print("   - Feature interactions (ratios, products)")
    print("   - Non-linear transformations")
    print("   - Rolling statistics on multiple timeframes")
    print("📊 Consider feature selection to identify best X features")
    
    # === SPECIFIC FEATURE ANALYSIS ===
    print("\n6. DETAILED ANALYSIS OF TOP FEATURES")
    print("-" * 40)
    
    # Analyze top X features
    for feat in ['X17', 'X37', 'X39']:
        values = sample_df[feat]
        print(f"\n{feat}:")
        print(f"  Range: [{values.min():.4f}, {values.max():.4f}]")
        print(f"  Mean: {values.mean():.4f}, Std: {values.std():.4f}")
        print(f"  Skewness: {values.skew():.4f}")
        
        # Check if it looks like a return or ratio
        if -1 <= values.min() and values.max() <= 1:
            print(f"  → Likely normalized/bounded feature")
        elif abs(values.mean()) < 0.1:
            print(f"  → Likely centered feature")
    
    return tech_correlations

def create_enhanced_features():
    """
    Create more sophisticated features based on insights
    """
    print("\n7. CREATING ENHANCED FEATURES")
    print("-" * 40)
    
    df = pd.read_parquet('train_with_technical_indicators.parquet')
    
    # More sophisticated features
    print("Adding enhanced features...")
    
    # Multi-timeframe features
    for window in [15, 30, 60, 120]:  # 15min to 2hr windows
        df[f'volume_trend_{window}'] = df['volume'].rolling(window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == window else np.nan
        )
        
        df[f'price_momentum_{window}'] = (df['mid_price'] / df['mid_price'].shift(window) - 1)
        
        df[f'volatility_ratio_{window}'] = (
            df['price_return_1'].rolling(window).std() / 
            df['price_return_1'].rolling(window*2).std()
        )
    
    # Feature interactions
    df['volume_price_interaction'] = df['volume'] * df['price_return_1']
    df['spread_volume_interaction'] = df['bid_ask_spread'] * df['volume']
    df['pressure_momentum_interaction'] = df['buy_pressure'] * df['price_return_5']
    
    # Non-linear transformations
    df['log_volume'] = np.log(df['volume'] + 1)
    df['sqrt_spread'] = np.sqrt(np.abs(df['bid_ask_spread']))
    
    print(f"Enhanced dataset: {df.shape[1]} total features")
    
    # Quick correlation check on new features
    new_features = [col for col in df.columns if any(x in col for x in 
                   ['trend_', 'momentum_', 'ratio_', 'interaction', 'log_', 'sqrt_'])]
    
    print(f"Created {len(new_features)} enhanced features")
    
    # Save enhanced dataset
    df.to_parquet('train_fully_enhanced.parquet')
    print("Saved fully enhanced dataset")
    
    return df

def main():
    """
    Main analysis function
    """
    # Detailed analysis
    tech_correlations = detailed_feature_analysis()
    
    # Create enhanced features
    enhanced_df = create_enhanced_features()
    
    print("\n" + "="*60)
    print("ANALYSIS COMPLETE!")
    print("="*60)
    print("\nKey Takeaways:")
    print("1. X features are much more predictive than basic technical indicators")
    print("2. Focus modeling efforts on X features (X17, X37, X39, etc.)")
    print("3. Use technical features as supplementary information")
    print("4. Consider feature selection to identify the best subset")
    print("\nFiles created:")
    print("- train_with_technical_indicators.parquet")
    print("- train_fully_enhanced.parquet")
    
    return tech_correlations, enhanced_df

if __name__ == "__main__":
    correlations, enhanced_data = main()
