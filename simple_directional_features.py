#!/usr/bin/env python3
"""
Simple Price Direction Classification Features
Create percentage changes and directional targets without plotting
"""

import pandas as pd
import numpy as np
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def create_price_proxy(df):
    """
    Create price proxy from market data since we don't have actual prices
    """
    print("Creating price proxy from market data...")
    
    # Multiple price proxies to test
    df['mid_price'] = (df['bid_qty'] + df['ask_qty']) / 2
    df['weighted_price'] = (df['buy_qty'] * df['bid_qty'] + df['sell_qty'] * df['ask_qty']) / (df['buy_qty'] + df['sell_qty'] + 1e-8)
    
    # Use mid_price as primary proxy (most common approach)
    df['price_proxy'] = df['mid_price']
    
    print(f"Price proxy stats: mean={df['price_proxy'].mean():.4f}, std={df['price_proxy'].std():.4f}")
    
    return df

def calculate_percentage_changes(df, periods=[1, 2, 3, 5, 10, 15, 30]):
    """
    Calculate percentage changes for price and volume over multiple periods
    """
    print(f"Calculating percentage changes for periods: {periods}")
    
    # Price percentage changes
    for period in periods:
        df[f'price_pct_change_{period}'] = df['price_proxy'].pct_change(periods=period) * 100
        df[f'volume_pct_change_{period}'] = df['volume'].pct_change(periods=period) * 100
        
        # Also calculate log returns (more stable for financial data)
        df[f'price_log_return_{period}'] = np.log(df['price_proxy'] / df['price_proxy'].shift(period)) * 100
        df[f'volume_log_return_{period}'] = np.log(df['volume'] / df['volume'].shift(period)) * 100
    
    print(f"Created {len(periods) * 4} percentage change features")
    return df

def create_directional_targets(df, periods=[1, 2, 3, 5, 10], threshold=0.01):
    """
    Create directional classification targets
    1 = positive return, 0 = neutral (small change), -1 = negative return
    """
    print(f"Creating directional targets with threshold: {threshold}%")
    
    for period in periods:
        pct_change_col = f'price_pct_change_{period}'
        target_col = f'direction_target_{period}'
        
        if pct_change_col in df.columns:
            # Create directional targets
            conditions = [
                df[pct_change_col] > threshold,    # Positive return
                df[pct_change_col] < -threshold,   # Negative return
            ]
            choices = [1, -1]
            
            df[target_col] = np.select(conditions, choices, default=0)
            
            # Also create binary targets (positive vs not positive)
            df[f'binary_target_{period}'] = (df[pct_change_col] > threshold).astype(int)
    
    print(f"Created {len(periods) * 2} directional target features")
    return df

def analyze_directional_targets(df):
    """
    Analyze the distribution and characteristics of directional targets
    """
    print("\nANALYZING DIRECTIONAL TARGETS")
    print("="*50)
    
    # Analyze target distributions
    periods = [1, 2, 3, 5, 10]
    
    for period in periods:
        target_col = f'direction_target_{period}'
        pct_col = f'price_pct_change_{period}'
        
        if target_col in df.columns:
            target_counts = df[target_col].value_counts().sort_index()
            total = target_counts.sum()
            
            print(f"\nPeriod {period} minutes:")
            print(f"  Negative (-1): {target_counts.get(-1, 0):6,} ({target_counts.get(-1, 0)/total*100:5.1f}%)")
            print(f"  Neutral  ( 0): {target_counts.get(0, 0):6,} ({target_counts.get(0, 0)/total*100:5.1f}%)")
            print(f"  Positive ( 1): {target_counts.get(1, 0):6,} ({target_counts.get(1, 0)/total*100:5.1f}%)")
            
            # Calculate statistics for percentage changes
            pct_changes = df[pct_col].dropna()
            print(f"  Price change stats: mean={pct_changes.mean():.4f}%, std={pct_changes.std():.4f}%")
            print(f"  Range: [{pct_changes.min():.2f}%, {pct_changes.max():.2f}%]")

def correlation_analysis_with_directions(df):
    """
    Analyze correlations between X features and directional targets
    """
    print("\nCORRELATION ANALYSIS WITH DIRECTIONAL TARGETS")
    print("="*50)
    
    # Get X features
    x_features = [col for col in df.columns if col.startswith('X')][:50]  # First 50 for speed
    
    # Analyze correlations with different target periods
    periods = [1, 2, 5, 10]
    
    results = {}
    
    for period in periods:
        target_col = f'direction_target_{period}'
        binary_col = f'binary_target_{period}'
        
        if target_col in df.columns:
            print(f"\nPeriod {period} minutes:")
            
            # Sample for faster computation
            sample_df = df.iloc[:10000]
            
            correlations = []
            for x_feat in x_features:
                try:
                    # Correlation with directional target (-1, 0, 1)
                    clean_data = sample_df[[x_feat, target_col]].dropna()
                    if len(clean_data) > 100:
                        corr_dir, p_dir = pearsonr(clean_data[x_feat], clean_data[target_col])
                        
                        # Correlation with binary target (0, 1)
                        clean_data_bin = sample_df[[x_feat, binary_col]].dropna()
                        corr_bin, p_bin = pearsonr(clean_data_bin[x_feat], clean_data_bin[binary_col])
                        
                        correlations.append({
                            'feature': x_feat,
                            'directional_corr': corr_dir,
                            'binary_corr': corr_bin,
                            'abs_directional': abs(corr_dir),
                            'abs_binary': abs(corr_bin)
                        })
                except:
                    continue
            
            # Sort by absolute directional correlation
            correlations.sort(key=lambda x: x['abs_directional'], reverse=True)
            
            print("Top 10 X features for directional prediction:")
            for i, corr_info in enumerate(correlations[:10]):
                print(f"{i+1:2d}. {corr_info['feature']:<6} | Dir: {corr_info['directional_corr']:6.3f} | Bin: {corr_info['binary_corr']:6.3f}")
            
            results[period] = correlations
    
    return results

def create_enhanced_volume_features(df):
    """
    Create enhanced volume-based features for directional prediction
    """
    print("\nCreating enhanced volume features...")
    
    # Volume momentum
    for period in [3, 5, 10, 20]:
        df[f'volume_momentum_{period}'] = df['volume'] / df['volume'].rolling(period).mean() - 1
        df[f'volume_acceleration_{period}'] = df[f'volume_momentum_{period}'].diff()
    
    # Buy/sell pressure changes
    df['buy_pressure'] = df['buy_qty'] / (df['volume'] + 1e-8)
    df['sell_pressure'] = df['sell_qty'] / (df['volume'] + 1e-8)
    
    for period in [2, 5, 10]:
        df[f'buy_pressure_change_{period}'] = df['buy_pressure'].diff(period)
        df[f'sell_pressure_change_{period}'] = df['sell_pressure'].diff(period)
        df[f'pressure_shift_{period}'] = df[f'buy_pressure_change_{period}'] - df[f'sell_pressure_change_{period}']
    
    # Order flow imbalance
    df['order_imbalance'] = (df['buy_qty'] - df['sell_qty']) / (df['volume'] + 1e-8)
    for period in [2, 5, 10]:
        df[f'imbalance_change_{period}'] = df['order_imbalance'].diff(period)
    
    print("Created enhanced volume and pressure features")
    return df

def analyze_feature_performance(df):
    """
    Analyze which features work best for directional prediction
    """
    print("\nFEATURE PERFORMANCE ANALYSIS")
    print("="*50)
    
    # Compare original label vs our directional targets
    sample_df = df.iloc[:10000]
    
    # Correlation between original label and our price changes
    for period in [1, 2, 5]:
        pct_col = f'price_pct_change_{period}'
        if pct_col in sample_df.columns:
            clean_data = sample_df[['label', pct_col]].dropna()
            if len(clean_data) > 100:
                corr, p_val = pearsonr(clean_data['label'], clean_data[pct_col])
                print(f"Original label vs {period}-min price change: r={corr:.4f} (p={p_val:.6f})")
    
    # Best X features for each directional target
    print("\nBest X features for directional prediction:")
    x_features = [col for col in df.columns if col.startswith('X')][:20]
    
    for period in [1, 5]:
        target_col = f'direction_target_{period}'
        if target_col in sample_df.columns:
            print(f"\n{period}-minute direction prediction:")
            
            correlations = []
            for x_feat in x_features:
                try:
                    clean_data = sample_df[[x_feat, target_col]].dropna()
                    if len(clean_data) > 100:
                        corr, p_val = pearsonr(clean_data[x_feat], clean_data[target_col])
                        correlations.append((x_feat, corr, abs(corr)))
                except:
                    continue
            
            correlations.sort(key=lambda x: x[2], reverse=True)
            for i, (feat, corr, abs_corr) in enumerate(correlations[:5]):
                print(f"  {i+1}. {feat}: r={corr:.4f}")

def main():
    """
    Main function to create directional features and analysis
    """
    print("PRICE DIRECTION CLASSIFICATION ANALYSIS")
    print("="*60)
    
    # Load data
    print("Loading data...")
    df = pd.read_parquet('train.parquet')
    print(f"Loaded: {df.shape}")
    
    # Create price proxy
    df = create_price_proxy(df)
    
    # Calculate percentage changes
    df = calculate_percentage_changes(df)
    
    # Create directional targets
    df = create_directional_targets(df, threshold=0.01)  # 0.01% threshold
    
    # Create enhanced volume features
    df = create_enhanced_volume_features(df)
    
    # Analyze directional targets
    analyze_directional_targets(df)
    
    # Correlation analysis
    correlation_results = correlation_analysis_with_directions(df)
    
    # Feature performance analysis
    analyze_feature_performance(df)
    
    # Save enhanced dataset
    print("\nSaving enhanced dataset with directional features...")
    df.to_parquet('train_with_directional_features.parquet')
    
    print("\n" + "="*60)
    print("DIRECTIONAL ANALYSIS COMPLETE!")
    print("="*60)
    print("\nKey Features Created:")
    print("✓ Price percentage changes (1, 2, 3, 5, 10, 15, 30 minute periods)")
    print("✓ Volume percentage changes (same periods)")
    print("✓ Directional targets: -1 (down), 0 (neutral), 1 (up)")
    print("✓ Binary targets: 0 (not positive), 1 (positive)")
    print("✓ Enhanced volume and pressure features")
    print("\nFiles created:")
    print("- train_with_directional_features.parquet")
    
    # Summary statistics
    print(f"\nDataset summary:")
    print(f"- Total features: {df.shape[1]}")
    print(f"- Directional targets: {len([col for col in df.columns if 'direction_target' in col])}")
    print(f"- Binary targets: {len([col for col in df.columns if 'binary_target' in col])}")
    print(f"- Percentage change features: {len([col for col in df.columns if 'pct_change' in col])}")
    
    return df, correlation_results

if __name__ == "__main__":
    enhanced_df, correlations = main()
