#!/usr/bin/env python3
"""
Label Target Analysis
Analyze the label column as our prediction target and create directional versions
"""

import pandas as pd
import numpy as np
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def analyze_label_target(df):
    """
    Comprehensive analysis of the label column (our target variable)
    """
    print("LABEL TARGET ANALYSIS")
    print("="*50)
    
    label = df['label']
    
    print("Label Statistics:")
    print(f"  Count: {label.count():,}")
    print(f"  Mean: {label.mean():.6f}")
    print(f"  Median: {label.median():.6f}")
    print(f"  Std: {label.std():.6f}")
    print(f"  Min: {label.min():.6f}")
    print(f"  Max: {label.max():.6f}")
    print(f"  Skewness: {label.skew():.6f}")
    print(f"  Kurtosis: {label.kurtosis():.6f}")
    
    # Percentiles
    percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
    print(f"\nLabel Percentiles:")
    for p in percentiles:
        print(f"  {p:2d}%: {label.quantile(p/100):.6f}")
    
    # Check for outliers
    Q1 = label.quantile(0.25)
    Q3 = label.quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    outliers = label[(label < lower_bound) | (label > upper_bound)]
    print(f"\nOutliers (IQR method): {len(outliers):,} ({len(outliers)/len(label)*100:.2f}%)")
    
    return label

def create_directional_targets_from_label(df, thresholds=[0.01, 0.05, 0.1]):
    """
    Create directional classification targets from the label column
    """
    print(f"\nCREATING DIRECTIONAL TARGETS FROM LABEL")
    print("="*50)
    
    for threshold in thresholds:
        # Create directional targets: 1 (positive), 0 (neutral), -1 (negative)
        target_col = f'label_direction_{int(threshold*100):02d}'
        
        conditions = [
            df['label'] > threshold,    # Positive
            df['label'] < -threshold,   # Negative
        ]
        choices = [1, -1]
        
        df[target_col] = np.select(conditions, choices, default=0)
        
        # Also create binary targets (positive vs not positive)
        df[f'label_binary_{int(threshold*100):02d}'] = (df['label'] > threshold).astype(int)
        
        # Analyze distribution
        target_counts = df[target_col].value_counts().sort_index()
        total = target_counts.sum()
        
        print(f"\nThreshold: {threshold:.3f}")
        print(f"  Negative (-1): {target_counts.get(-1, 0):6,} ({target_counts.get(-1, 0)/total*100:5.1f}%)")
        print(f"  Neutral  ( 0): {target_counts.get(0, 0):6,} ({target_counts.get(0, 0)/total*100:5.1f}%)")
        print(f"  Positive ( 1): {target_counts.get(1, 0):6,} ({target_counts.get(1, 0)/total*100:5.1f}%)")
    
    return df

def analyze_feature_correlations_with_label(df):
    """
    Analyze correlations between all features and the label target
    """
    print(f"\nFEATURE CORRELATIONS WITH LABEL TARGET")
    print("="*50)
    
    # Get different feature types
    market_features = ['bid_qty', 'ask_qty', 'buy_qty', 'sell_qty', 'volume']
    x_features = [col for col in df.columns if col.startswith('X')]
    
    # Sample for faster computation
    sample_df = df.iloc[:10000]
    
    # Market features correlation
    print("Market Features Correlation with Label:")
    market_correlations = []
    for feat in market_features:
        try:
            clean_data = sample_df[[feat, 'label']].dropna()
            if len(clean_data) > 100:
                corr, p_val = pearsonr(clean_data[feat], clean_data['label'])
                market_correlations.append((feat, corr, abs(corr), p_val))
                print(f"  {feat:<12}: r={corr:7.4f} (p={p_val:.6f})")
        except:
            continue
    
    # X features correlation (top 20 for speed)
    print(f"\nTop X Features Correlation with Label:")
    x_correlations = []
    for feat in x_features[:50]:  # First 50 X features
        try:
            clean_data = sample_df[[feat, 'label']].dropna()
            if len(clean_data) > 100:
                corr, p_val = pearsonr(clean_data[feat], clean_data['label'])
                x_correlations.append((feat, corr, abs(corr), p_val))
        except:
            continue
    
    # Sort by absolute correlation
    x_correlations.sort(key=lambda x: x[2], reverse=True)
    
    print("Top 15 X features:")
    for i, (feat, corr, abs_corr, p_val) in enumerate(x_correlations[:15]):
        print(f"  {i+1:2d}. {feat:<6}: r={corr:7.4f} (p={p_val:.6f})")
    
    return market_correlations, x_correlations

def analyze_directional_prediction_performance(df):
    """
    Analyze how well features predict directional targets
    """
    print(f"\nDIRECTIONAL PREDICTION ANALYSIS")
    print("="*50)
    
    # Get top X features from previous analysis
    top_x_features = ['X17', 'X37', 'X39', 'X35', 'X185', 'X41', 'X188', 'X33', 'X191']
    
    sample_df = df.iloc[:10000]
    
    # Test different thresholds
    thresholds = [0.01, 0.05, 0.1]
    
    for threshold in thresholds:
        direction_col = f'label_direction_{int(threshold*100):02d}'
        binary_col = f'label_binary_{int(threshold*100):02d}'
        
        if direction_col in df.columns:
            print(f"\nThreshold: {threshold:.3f}")
            print("Top features for directional prediction:")
            
            correlations = []
            for feat in top_x_features:
                try:
                    # Directional correlation
                    clean_data = sample_df[[feat, direction_col]].dropna()
                    if len(clean_data) > 100:
                        corr_dir, p_dir = pearsonr(clean_data[feat], clean_data[direction_col])
                        
                        # Binary correlation
                        clean_data_bin = sample_df[[feat, binary_col]].dropna()
                        corr_bin, p_bin = pearsonr(clean_data_bin[feat], clean_data_bin[binary_col])
                        
                        correlations.append((feat, corr_dir, corr_bin, abs(corr_dir)))
                except:
                    continue
            
            correlations.sort(key=lambda x: x[3], reverse=True)
            
            for i, (feat, corr_dir, corr_bin, abs_corr) in enumerate(correlations[:8]):
                print(f"  {i+1}. {feat}: Dir={corr_dir:6.3f}, Bin={corr_bin:6.3f}")

def create_additional_features(df):
    """
    Create additional features that might help with prediction
    """
    print(f"\nCREATING ADDITIONAL FEATURES")
    print("="*50)
    
    # Market microstructure features
    df['bid_ask_spread'] = df['ask_qty'] - df['bid_qty']
    df['bid_ask_ratio'] = df['bid_qty'] / (df['ask_qty'] + 1e-8)
    df['buy_sell_imbalance'] = df['buy_qty'] - df['sell_qty']
    df['buy_sell_ratio'] = df['buy_qty'] / (df['sell_qty'] + 1e-8)
    
    # Volume features
    df['buy_pressure'] = df['buy_qty'] / (df['volume'] + 1e-8)
    df['sell_pressure'] = df['sell_qty'] / (df['volume'] + 1e-8)
    df['net_pressure'] = df['buy_pressure'] - df['sell_pressure']
    
    # Rolling features (short windows for minute data)
    windows = [3, 5, 10]
    for window in windows:
        df[f'volume_ma_{window}'] = df['volume'].rolling(window).mean()
        df[f'buy_pressure_ma_{window}'] = df['buy_pressure'].rolling(window).mean()
        df[f'imbalance_ma_{window}'] = df['buy_sell_imbalance'].rolling(window).mean()
    
    # Lag features of label (for time series prediction)
    for lag in [1, 2, 3, 5]:
        df[f'label_lag_{lag}'] = df['label'].shift(lag)
    
    new_features = [col for col in df.columns if any(x in col for x in 
                   ['spread', 'ratio', 'imbalance', 'pressure', '_ma_', 'lag_'])]
    
    print(f"Created {len(new_features)} additional features:")
    for feat in new_features[:10]:  # Show first 10
        print(f"  - {feat}")
    if len(new_features) > 10:
        print(f"  ... and {len(new_features) - 10} more")
    
    return df

def main():
    """
    Main analysis function
    """
    print("LABEL TARGET COMPREHENSIVE ANALYSIS")
    print("="*60)
    
    # Load data
    print("Loading data...")
    df = pd.read_parquet('train.parquet')
    print(f"Loaded: {df.shape}")
    
    # Analyze label target
    label_stats = analyze_label_target(df)
    
    # Create directional targets from label
    df = create_directional_targets_from_label(df)
    
    # Analyze feature correlations
    market_corr, x_corr = analyze_feature_correlations_with_label(df)
    
    # Create additional features
    df = create_additional_features(df)
    
    # Analyze directional prediction performance
    analyze_directional_prediction_performance(df)
    
    # Save enhanced dataset
    print(f"\nSaving enhanced dataset...")
    df.to_parquet('train_with_label_analysis.parquet')
    
    print("\n" + "="*60)
    print("LABEL TARGET ANALYSIS COMPLETE!")
    print("="*60)
    
    print("\nKey Insights:")
    print("✓ Label is our target variable (continuous values)")
    print("✓ Created directional versions for classification")
    print("✓ X features show strong correlations with label")
    print("✓ Added market microstructure features")
    print("✓ Ready for both regression and classification modeling")
    
    print(f"\nDataset summary:")
    print(f"- Total features: {df.shape[1]}")
    print(f"- Original target: label (continuous)")
    print(f"- Directional targets: {len([col for col in df.columns if 'label_direction' in col])}")
    print(f"- Binary targets: {len([col for col in df.columns if 'label_binary' in col])}")
    
    print("\nFiles created:")
    print("- train_with_label_analysis.parquet")
    
    return df, x_corr

if __name__ == "__main__":
    enhanced_df, correlations = main()
